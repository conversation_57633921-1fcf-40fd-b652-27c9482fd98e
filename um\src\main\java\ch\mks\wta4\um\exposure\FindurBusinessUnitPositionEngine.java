package ch.mks.wta4.um.exposure;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mkspamp.eventbus.model.BuBalance;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.TradeCreatedEvent;

import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.exposure.SingleThreadDispatcher.SingleThreadDispatcherListener;

public class FindurBusinessUnitPositionEngine extends AbstractWTA4Service implements IBusinessUnitPositionEngine, SingleThreadDispatcherListener<PositionContribution>{
    private final static Logger LOG = LoggerFactory.getLogger(FindurBusinessUnitPositionEngine.class);

    private final IFindurAPIGatewayClient findurAPIGatewayClient;
    private final IConfiguration configuration;

    private final FindurBusinessUnitPositionEngineCalculator calculator;
    
    private final List<IBusinessUnitPositionEngineListener> listeners = new CopyOnWriteArrayList<>();
    private final Map<String, InternalBusinessUnitPosition> positions = new ConcurrentHashMap<>();
    private final Map<String, Cache<String,PositionContribution>> lastContributions = new ConcurrentHashMap<>();
    private final Map<String,Object> updateLock = new ConcurrentHashMap<>();
    
    private final ScheduledExecutorService findurRefreshScheduledExecutorService;
    private final ExecutorService workerExecutorService;

    private final long positionContributionTimeToLive; // long enough to hold deals arriving while updating from findur, short enough to reduce performance impact
    private final long findurPositionRefreshPeriodSeconds;
    private final long findurPositionRefreshPeriodMillis;
    private final long findurPositionRefreshPollingMillis; 
    
    private final FindurBusinessUnitPositionEngineMetrics metrics;
    private final SingleThreadDispatcher<PositionContribution> updateDispatcher;

    private FindurEventConsumer findurEventConsumer;
    
    public FindurBusinessUnitPositionEngine(IFindurAPIGatewayClient findurAPIGatewayClient, IConfiguration configuration) { 
        this.findurAPIGatewayClient = findurAPIGatewayClient;
        this.configuration = configuration;
        
        this.positionContributionTimeToLive = configuration.getFindurPositionContributionCacheTimeToLiveMillis();
        this.findurPositionRefreshPeriodSeconds = configuration.getFindurPositionRefreshPeriodSeconds();
        this.findurPositionRefreshPeriodMillis = findurPositionRefreshPeriodSeconds * 1000;
        this.findurPositionRefreshPollingMillis = (long)(findurPositionRefreshPeriodMillis/100);
        
        this.calculator = new FindurBusinessUnitPositionEngineCalculator();
        this.findurRefreshScheduledExecutorService = Executors.newScheduledThreadPool(1, ThreadUtils.getThreadFactory("wta4-findur-bu-position-engine-scheduler"));
        this.workerExecutorService = Executors.newCachedThreadPool(ThreadUtils.getThreadFactory("wta4-findur-bu-position-engine-worker")); // hits findur
        this.updateDispatcher = new SingleThreadDispatcher<>("wta4-findur-bu-positon-dispatcher", this, LOG);
        this.metrics = new FindurBusinessUnitPositionEngineMetrics();
    }

    @Override
    protected void startUp() throws Exception {

        if ( ! configuration.isExposureFeatureEnabled() ) {
            LOG.info("startUp - exposure feature is not enabled. Doing nothing");
            return;
        }

        LOG.info("startUp -> positionContributionTimeToLive={}, findurPositionRefreshPeriodSeconds={}", positionContributionTimeToLive, findurPositionRefreshPeriodSeconds);

        // Initialize and start the FindurEventConsumer
        findurEventConsumer = new FindurEventConsumer(configuration);
        findurEventConsumer.addListener(event -> onFindurEvent(event));
        findurEventConsumer.startUp();

        findurRefreshScheduledExecutorService.scheduleAtFixedRate(() -> pollToRefreshFindurBalances(), 0, findurPositionRefreshPollingMillis, TimeUnit.MILLISECONDS);
        updateDispatcher.start();
        LOG.info("startUp <-");
    }

    @Override
    protected void shutDown() throws Exception {

        if ( ! configuration.isExposureFeatureEnabled() ) {
            LOG.info("shutDown - exposure feature is not enabled. Doing nothing");
            return;
        }

        LOG.info("shutDown ->");

        // Stop the FindurEventConsumer first
        if (findurEventConsumer != null) {
            try {
                findurEventConsumer.shutDown();
                LOG.info("shutDown - FindurEventConsumer stopped successfully");
            } catch (Exception e) {
                LOG.error("shutDown - error stopping FindurEventConsumer", e);
            }
        }

        updateDispatcher.stop();
        findurRefreshScheduledExecutorService.shutdownNow();
        findurRefreshScheduledExecutorService.awaitTermination(10, TimeUnit.SECONDS);

        workerExecutorService.shutdownNow();
        workerExecutorService.awaitTermination(10, TimeUnit.SECONDS);
        LOG.info("shutDown <-");
    }

    private void pollToRefreshFindurBalances() {
        try {
            LOG.debug("pollToRefreshFindurBalances ->");
            long now = System.currentTimeMillis();
                       
            positions.values().stream()
                .filter(internalPosition -> (
                        ! internalPosition.isUpdating ) 
                        && internalPosition.lastUpdatedFromFindur + findurPositionRefreshPeriodMillis <= now )
                .forEach(internalPosition -> {
                    internalPosition.isUpdating  = true;
                    workerExecutorService.execute(() -> {
                        try {
                            LOG.info("pollToRefreshFindurBalances - requesting new balances from findur for bu={}", internalPosition.businessUnitPosition.getBuId());
                            InternalBusinessUnitPosition newInternalPosition = refreshInternalBusinessUnitPositionFromFindur(internalPosition.businessUnitPosition.getBuId());
                            notifyListeners(newInternalPosition.businessUnitPosition);
                        } catch (Exception e) {
                            LOG.error("pollToRefreshFindurBalances - buId={}", internalPosition.businessUnitPosition.getBuId(), e);
                        }
                    });
            });
            
            LOG.debug("pollToRefreshFindurBalances <-");
        } catch (Exception e) {
            LOG.error("pollToRefreshFindurBalances - ", e);
        }
    }
    
    @Override
    public BusinessUnitPosition getBusinessUnitPosition(String buId) {
        LOG.info("getBusinessUnitPosition -> buId={}", buId);
        InternalBusinessUnitPosition internalBusinessUnitPosition = getInternalBusinessUnitPosition(buId);
        BusinessUnitPosition businessUnitPosition = internalBusinessUnitPosition.businessUnitPosition;
        LOG.info("getBusinessUnitPosition <- returns {}", businessUnitPosition );
        return businessUnitPosition;
    }
    
    private InternalBusinessUnitPosition getInternalBusinessUnitPosition(String buId) {
        InternalBusinessUnitPosition internalBusinessUnitPosition = positions.get(buId);
        if ( internalBusinessUnitPosition == null ) {
            internalBusinessUnitPosition = refreshInternalBusinessUnitPositionFromFindur(buId);
        }
        return internalBusinessUnitPosition;
    }

    private InternalBusinessUnitPosition refreshInternalBusinessUnitPositionFromFindur(String buId) {

        LOG.info("refreshInternalBusinessUnitPositionFromFindur -> buId={}", buId);
        String findurBUId = resolveFindurBuId(buId);

        long requestTimestamp = System.currentTimeMillis();
        BuBalance findurBalances = requestFindurBalances(findurBUId);
        metrics.updateFindurResponseTime(buId, requestTimestamp);
        
        if ( findurBalances == null ) {
            //something bad happened calling Findur
            InternalBusinessUnitPosition currentInternalBusinessUnitPosition = positions.get(buId);

            if ( currentInternalBusinessUnitPosition == null ) {
                // we do not have anything in this side
                LOG.error("refreshInternalBusinessUnitPositionFromFindur - buId={}. Unable to retrieve Findur balances and no current position. Simulating empty balance from Findur", buId);
                findurBalances = new BuBalance();
            } else {
                // we stick with what we have
                LOG.error("refreshInternalBusinessUnitPositionFromFindur <- buId={}. Unable to retrieve Findur balances. No refresh, sticking with what we already have. currentInternalBusinessUnitPosition={}", buId, currentInternalBusinessUnitPosition);
                currentInternalBusinessUnitPosition.isUpdating = false;
                currentInternalBusinessUnitPosition.lastUpdatedFromFindur = requestTimestamp; //this is to prevent from requesting again on next poll
                return currentInternalBusinessUnitPosition;
            }
        }
        
        long processingStartTimestamp = System.currentTimeMillis();
        InternalBusinessUnitPosition internalBusinessUnitPosition = computeBusinessUnitPositionFromFindurBalances(buId, findurBalances);
        metrics.updateFindurResponseProcessingTime(buId, processingStartTimestamp);
        LOG.info("refreshInternalBusinessUnitPositionFromFindur <- buId={}, updated internal position={}", buId, internalBusinessUnitPosition);
        return internalBusinessUnitPosition;
    }

    private BuBalance requestFindurBalances(String findurBUId) {
        try {
            LOG.info("requestFindurBalances -> findurBUId={}", findurBUId);
            
            BuBalance buBalance = null;
            
            // FIXME HACK WTADEV-253 exclude porfolio BU's
            if ( isValidFindurBUId(findurBUId) ) {
                buBalance = findurAPIGatewayClient.getCustomerBalance(findurBUId);    
            } else {
                LOG.info("requestFindurBalances - findurBUId is not valid. Returning null", findurBUId);
            }
            
            
            LOG.info("requestFindurBalances <- buBalance={}", buBalance);    
            return buBalance;
        } catch (Exception e) {
            LOG.error("requestFindurBalances - findurBUId={}. Returning null", findurBUId, e);
            return null;
        }
    }

    private boolean isValidFindurBUId(String findurBUId) {
        try {
            int aux = Integer.valueOf(findurBUId);
            return aux != 0;
        } catch (Exception e) {
            return false;    
        }
    }

    private InternalBusinessUnitPosition computeBusinessUnitPositionFromFindurBalances(String buId, BuBalance findurBuBalance) {
        synchronized (getUpdateLock(buId)) {
            LOG.info("computeBusinessUnitPositionFromFindurBalances -> buId={}, findurBalances={}", buId, findurBuBalance);
            BusinessUnitPosition businessUnitPosition = calculator.flattenFindurBalances(buId, findurBuBalance);
            applyLatestDeals(buId, businessUnitPosition, findurBuBalance);
            
            //update times
            Long now = System.currentTimeMillis();
            businessUnitPosition.setLastBaseUpdate(now);
            businessUnitPosition.setLastProjectedUpdate(now);
            
            InternalBusinessUnitPosition result = new InternalBusinessUnitPosition(businessUnitPosition, findurBuBalance);
            positions.put(buId, result);
            LOG.info("computeBusinessUnitPositionFromFindurBalances <- buId={}, returns {}", buId, result );
            return result;
        }
    }

    private void applyLatestDeals(String buId, BusinessUnitPosition businessUnitPosition, BuBalance findurBuBalance) {
        
        if ( lastContributions.containsKey(buId)) {
            lastContributions.get(buId).asMap().values().forEach(positionContribution -> {
                if ( ! isOrderAlreadyAccountedFor(positionContribution, findurBuBalance) ) {
                    processPositionContribution(positionContribution, businessUnitPosition);
                    LOG.info("applyLatestDeals - applied missing positionContribution={} to businessUnitPosition={}", positionContribution, businessUnitPosition);
                }
            });
        }
    }

    private String resolveFindurBuId(String buId) {
        LOG.info("resolveFindurBuId -> buId={}", buId);
        BusinessUnit bu = configuration.getBusinessUnit(buId);
        
        if ( bu == null || bu.getFindurId() == null || bu.getFindurId().isEmpty() ) {
            LOG.error("resolveFindurBuId - could not resolve findurBUId for buId={}, bu={}", buId, bu);
            throw new RuntimeException(String.format("resolveFindurBuId - could not resolve findurBUId for buId=%s, bu=%s", buId, bu));
        }
        
        String findurBUId = bu.getFindurId(); 
        LOG.info("resolveFindurBuId <- buId={}, findurBUId={}", buId, findurBUId);
        return findurBUId;
    }

    @Override
    public void onWTADeal(Order order) {
        LOG.info("onWTADeal -> order={}", order);
        onPositionContributions(calculator.computeWTADealPositionContributions(order, configuration));
        LOG.info("onWTADeal <-");
    }
    
    @Override
    public void onFindurEvent(Event findurEvent) {
       LOG.info("onFindurEvent -> findurEvent={}", findurEvent);
       switch (findurEvent.getType()) {
       case TRADE_CREATED:
           metrics.incrementEventsBySource(findurEvent.getSrc());
           onPositionContributions(computePositionContributions((TradeCreatedEvent)findurEvent));
           break;
       case OPTION_EXERCISED:
       case OPTION_EXPIRED:
       case TRADE_CANCELED:
       case TRADE_UPDATED:
       default:
           metrics.incrementIgnoredEvents();
           LOG.info("onFindurEvent <- ignoring findurEvent={}", findurEvent);
       }

       LOG.info("onFindurEvent <-");
    }
    
    private void onPositionContributions(List<PositionContribution> positionContributions) {
        LOG.info("onPositionContributions -> positionContributions={}", positionContributions);
        positionContributions.forEach(positionContribution -> updateDispatcher.queue(positionContribution));
        LOG.info("onPositionContributions <-");
        
    }
    
    private List<PositionContribution> computePositionContributions(TradeCreatedEvent tradeCreatedEvent){
        LOG.info("computePositionContributions -> findurEvent={}", tradeCreatedEvent);
        List<PositionContribution> contributions = calculator.computeTradeEventContributions(tradeCreatedEvent);
        LOG.info("computePositionContributions <- findurEvent={}, contributions={}", tradeCreatedEvent, contributions);
        return contributions;
    }
    
    // called as dispatched by the queue
    @Override
    public void onUpdate(PositionContribution positionContribution) {
        InternalBusinessUnitPosition internalPosition = getInternalBusinessUnitPosition(positionContribution.getBusinessUnitId());
        if (internalPosition == null) {
            LOG.error("onUpdate - no position found for bu in positionContribution={}", positionContribution);
            return;
        }
        processPositionContribution(positionContribution, internalPosition.businessUnitPosition);
        notifyListeners(internalPosition.businessUnitPosition);
        internalPosition.businessUnitPosition.setLastProjectedUpdate(System.currentTimeMillis());
    }
    
    private void processPositionContribution(PositionContribution positionContribution, BusinessUnitPosition businessUnitPosition) {
        if (!configuration.isExposureFeatureEnabled()) {
            LOG.info("processPositionContribution - exposure feature is not enabled. positionContribution NOT processed. positionContribution={}", positionContribution);
            return;
        }

        String buId = positionContribution.getBusinessUnitId();
        synchronized (getUpdateLock(buId)) {
            calculator.updateBusinessUnitPositonWithContribution(businessUnitPosition, positionContribution);
            lastContributions.computeIfAbsent(buId, b -> createCache()).put(getCacheKey(positionContribution), positionContribution);
        }
    }

    private String getCacheKey(PositionContribution positionContribution) {
       return positionContribution.getOriginalDealId() + "-" + positionContribution.getCurrencyId();
    }

    private synchronized Object getUpdateLock(String buId) {
        return updateLock.computeIfAbsent(buId, b -> new Object());
    }

    private boolean isOrderAlreadyAccountedFor(PositionContribution positionContribution, BuBalance findurBuBalance) {
        
        String dealId = positionContribution.getOriginalDealId();
        LocalDate orderTradeDate = positionContribution.getTradeDate();
        String currencyId = positionContribution.getCurrencyId();
        
        boolean isOrderTradeDateInThePast = findurBuBalance.getAccountBalances().stream()
            .filter(ab -> ab.getCurrency().equals(currencyId ) )
            .filter(ab -> ab != null)
            .allMatch(ab -> orderTradeDate.isBefore(ab.getTradeDate().getDate())); // returns true if the stream is empty
        
        if ( isOrderTradeDateInThePast ) { // it's in the past and already accounted for in the balances
            removeContributionFromHistory(positionContribution);
            return true;
        }
        
        return findurBuBalance.getAccountBalances()
            .stream()
            .filter(ab -> ab.getCurrency().equals(currencyId) )
            .map(ab -> ab.getFutureCashFlows())
            .flatMap(Collection::stream)
            .map(futureCashFlow -> futureCashFlow.getTradeSrcId())
            .anyMatch(execId -> dealId.equals(execId));
        
    }

    private void removeContributionFromHistory(PositionContribution positionContribution) {
        LOG.info("removeContributionFromHistory - removing positionContribution from history, positionContribution={}", positionContribution);
        lastContributions.get(positionContribution.getBusinessUnitId()).invalidate(getCacheKey(positionContribution));
    }

    private Cache<String,PositionContribution> createCache() {
        return CacheBuilder.newBuilder().
                expireAfterWrite(positionContributionTimeToLive, TimeUnit.MILLISECONDS).
                build();
    }

    private void notifyListeners(BusinessUnitPosition businessUnitPosition) {
        listeners.forEach(l -> {
            try {
                l.onBusinessUnitPositionUpdate(businessUnitPosition);
                LOG.info("notifyListeners - done with {}", l.hashCode());
            } catch (Exception e) {
                LOG.error("notifyListeners - ", e);
            }

        });
    }
    
    @Override
    public void addListener(IBusinessUnitPositionEngineListener listener) {
        LOG.info("addListener - adding {}", listener.hashCode());
        listeners.add(listener);
    }

    @Override
    public void removeListener(IBusinessUnitPositionEngineListener listener) {
        listeners.remove(listener);
    }

    private static class InternalBusinessUnitPosition{
        public boolean isUpdating = false;
        final BusinessUnitPosition businessUnitPosition;
        final BuBalance findurBuBalance;
        public long lastUpdatedFromFindur;
            
        public InternalBusinessUnitPosition(BusinessUnitPosition businessUnitPosition, BuBalance findurBuBalances) {
            this.businessUnitPosition = businessUnitPosition;
            this.findurBuBalance = findurBuBalances;
            this.isUpdating = false;
            this.lastUpdatedFromFindur = System.currentTimeMillis();
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("InternalBusinessUnitPosition [isUpdating=").append(isUpdating).append(", businessUnitPosition=").append(businessUnitPosition)
                    .append(", findurBalances=").append(findurBuBalance).append(", lastUpdatedFromFindur=").append(new Date(lastUpdatedFromFindur)).append("]");
            return builder.toString();
        }
    }

}
