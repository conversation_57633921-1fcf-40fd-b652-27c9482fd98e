package ch.mks.wta4.wta4app.config;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import ch.mks.wta4.auth.IAuthProvider;
import ch.mks.wta4.auth.IOidcAuthProvider;
import ch.mks.wta4.common.email.EmailClient;
import ch.mks.wta4.common.metrics.MicrometerService;
import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.IFixSequenceNumberResetService;
import ch.mks.wta4.configuration.IJMXFixSessionService;
import ch.mks.wta4.configuration.IJMXMonitorService;
import ch.mks.wta4.configuration.ILFX2ITAConfiguration;
import ch.mks.wta4.configuration.IOidcConfiguration;
import ch.mks.wta4.findurfixclient.FindurFixClient;
import ch.mks.wta4.findurfixclient.dto.Platform;
import ch.mks.wta4.ita.InternalTradingAPIService;
import ch.mks.wta4.ita.model.AuditLogBuilder;
import ch.mks.wta4.ita.simulator.ITASimulator;
import ch.mks.wta4.itafix.adapter.ITAFIXAdapter;
import ch.mks.wta4.itafix.service.APIUserStatusCheckService;
import ch.mks.wta4.itafix.service.FixSequenceNumberResetService;
import ch.mks.wta4.itafix.service.JMXFixSessionService;
import ch.mks.wta4.lfx2.LFX2InternalTradingAPIImpl;
import ch.mks.wta4.messaging.amq.ActiveMQLocalBroker;
import ch.mks.wta4.notification.IEmailNotificationService;
import ch.mks.wta4.notification.push.IPushNotificationService;
import ch.mks.wta4.notification.push.IUmPushNotificationService;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.position.IBackendPositionProvider;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.services.authentication.CommonUserPasswordAuthenticationProvider;
import ch.mks.wta4.services.authentication.KeycloakAuthenticationProvider;
import ch.mks.wta4.services.authentication.MicrosoftAuthenticationProvider;
import ch.mks.wta4.services.configuration.cache.CacheRefreshManager;
import ch.mks.wta4.services.monitor.HealthMonitorService;
import ch.mks.wta4.services.monitor.JMXFixAPIService;
import ch.mks.wta4.services.notification.EmailNotificationService;
import ch.mks.wta4.services.notification.push.FirebaseLegacyPushNotificationService;
import ch.mks.wta4.services.notification.push.UmPushNotificationService;
import ch.mks.wta4.services.position.FindurAPIGatewayClient;
import ch.mks.wta4.services.position.FindurPositionProvider;
import ch.mks.wta4.um.LocalUnallocatedModuleImpl;
import ch.mks.wta4.um.booking.AbstractBookingService;
import ch.mks.wta4.um.booking.BookingServiceImpl;
import ch.mks.wta4.um.booking.SimulatedBookingService;
import ch.mks.wta4.umgrpc.adapter.UMGRPCServer;


@Configuration
@ComponentScan("ch.mks.wta4")
public class WTA4Configuration {

    private final static Logger LOG = LoggerFactory.getLogger(WTA4Configuration.class);

    @Autowired
    @Qualifier("lfx2ITAConfigurationImpl")
    ILFX2ITAConfiguration lfx2ITAConfiguration;

    @Autowired
    @Qualifier("configurationCacheService")
    ICachedConfiguration cachedConfiguration;

    @Autowired
    IPersistenceManager persistenceManager;

    @Autowired
    IQueryService persistenceQueryService;

    @Qualifier("microsoftAuthenticationConfiguration")
    @Autowired
    IOidcConfiguration microsoftAuthenticationConfiguration;

    @Qualifier("keycloakAuthenticationConfiguration")
    @Autowired
    IOidcConfiguration keycloakAuthenticationConfiguration;

    @Value("${mail.smtp.host:}")
    private String smtpHost;

    @Value("${mail.smtp.port:}")
    private String smtpPort;

    @Value("${mail.smtp.from:<EMAIL>}")
    private String emailFrom;

    @Value("${mail.environment:dev}")
    private String emailEnvironment;

    @Value("${mail.tlsEnable:}")
    private String tlsEnable;

    @Value("${mail.tlsVersion:}")
    private String tlsVersion;

    @Value("${amq.connectorUrl:tcp://127.0.0.1:6666}")
    private String amqConnectorURL;

    @Value("${amq.stompConnectorUrl:stomp://127.0.0.1:7777}")
    private String amqSTOMPConnectorURL;

    @Value("${amq.storagePath:var/amq}")
    private String amqStoragePath;

    @Value("${email.override:}")
    private String emailOverride;

    @Value("${findur.webservice.url:}")
    private String findurWebserviceUrl;

    @Value("${findur.webservice.user:}")
    private String findurWebserviceUser;

    @Value("${findur.webservice.password:}")
    private String findurWebservicePassword;

    @Value("${findur.apigateway.baseurl:}")
    private String findurAPIGatewayBaseUrl;

    @Value("${findur.apigateway.apikey:}")
    private String findurAPIGatewayAPIKey;

    @Value("${bookingSupportEmail:}")
    private String bookingSupportEmail;

    @Value("${liquidity.provider.config:}")
    private String liquidityProviderConfig;

    @Value("${fix.adapter.config:}")
    private String fixAdapterConfig;

    @Value("${findur.booking.config:}")
    private String findurBookingConfig;

    @Value("${mdm.cache.refreshinterval:10000}")
    private long cacheRefreshInstructionInterval;

    @Value("${http.proxy.host:}")
    private String httpProxyHost;

    @Value("${http.proxy.port:}")
    private Integer httpProxyPort;

    @Value("${pushnotification.firebase.server.url:}")
    private String firebaseServerUrl;

    @Value("${pushnotification.firebase.server.authenticationJsonFile:}")
    private String authenticationJsonFile;

    /* GRPC */
    @Value("${grpc.host:127.0.0.1}")
    private String grpcHost;

    @Value("${grpc.port:9898}")
    private int grpcPort;

    @Value("#{${grpc.secretsByClientId:{'WTA4-UI':'default-secret', 'WTA4-DEALER-UI':'default-secret'}}}")
    private Map<String, String> grpcSecretsByClientId;

    @Value("#{${grpc.clientIdsAuthorizedForDealerControl:'WTA4-DEALER-UI'}}")
    private List<String> grpcClientIdsAuthorizedForDealerControl;

    @Value("${grpc.maxPingPeriodMillis:50000}")
    private long grpcMaxPingPeriodMillis;

    @Value("${localUnallocatedModule.timeout:50}")
    private int localUnallocatedModuleTimeout;

    @Value("${itaSimulator.ticksPerSecond:10}")
    private int itaSimulatorTicksPerSecond;
    
    @Value("${ita.fixadapter.dbstore:false}")
    private boolean isDBStore;
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    ApplicationContext applicationContext;

    @Bean
    @Qualifier("lpInternalTradingAPIImpl")
    public InternalTradingAPIService lpInternalTradingAPI() {

        if ( simulateLP() ) {
            return createLPSimulator();
        }

        return new LFX2InternalTradingAPIImpl(liquidityProviderConfig, lfx2ITAConfiguration, cachedConfiguration,emailNotificationService());

    }

    @Bean
	public ITAFIXAdapter fixAdapter() {
		try {
			return new ITAFIXAdapter(unallocatedModule(), cachedConfiguration, commonAuthenticationProvider(), isDBStore, dataSource);
		} catch (Exception e) {
			LOG.error("lpInternalTradingAPI - error creating ITAFixAdapter with file={}", fixAdapterConfig, e);
			throw new RuntimeException(
					String.format("lpInternalTradingAPI - error creating ITAFixAdapter with file=%s", fixAdapterConfig),
					e);
		}
	}
    
    
    @Bean
    public UMGRPCServer umGRPCAdapter() {
        return new UMGRPCServer(grpcHost, grpcPort, grpcSecretsByClientId, grpcClientIdsAuthorizedForDealerControl, grpcMaxPingPeriodMillis, unallocatedModule(), persistenceQueryService, cachedConfiguration, positionProvider(), commonAuthenticationProvider(), keycloakAuthenticationProvider(), microsoftAuthenticationProvider(), persistenceManager);
    }

    @Bean
    @Qualifier("commonUserPasswordAuthenticationProvider")
    public IAuthProvider commonAuthenticationProvider() {
        return new CommonUserPasswordAuthenticationProvider(cachedConfiguration, persistenceManager, emailNotificationService());
    }

    @Bean
    public IOidcAuthProvider microsoftAuthenticationProvider() {
        return new MicrosoftAuthenticationProvider(microsoftAuthenticationConfiguration, cachedConfiguration, commonAuthenticationProvider(), httpProxyHost, httpProxyPort);
    }

    @Bean
    public IOidcAuthProvider keycloakAuthenticationProvider() {
        return new KeycloakAuthenticationProvider(keycloakAuthenticationConfiguration, cachedConfiguration, commonAuthenticationProvider(), httpProxyHost, httpProxyPort);
    }

    @Bean
    public ActiveMQLocalBroker activeMQLocalBroker() {
        return new ActiveMQLocalBroker(amqConnectorURL, amqSTOMPConnectorURL, amqStoragePath);
    }

    @Bean
    @Qualifier("localUnallocatedModuleImpl")
    public LocalUnallocatedModuleImpl unallocatedModule() {
        return new LocalUnallocatedModuleImpl(
                cachedConfiguration,
                lpInternalTradingAPI(),
                persistenceManager,
                persistenceQueryService,
                bookingService(),
                emailNotificationService(),
                umPushNotificationService(),
                findurAPIGatewayClient());
    }

    @Bean
    @Qualifier("emailNotificationService")
    public IEmailNotificationService emailNotificationService() {
        return new EmailNotificationService(emailClient(), templateConfiguration(), cachedConfiguration, emailOverride);
    }

    @Bean
    public IUmPushNotificationService umPushNotificationService() {
        return new UmPushNotificationService(pushNotificationService(), cachedConfiguration);
    }

    @Bean
    public IPushNotificationService pushNotificationService() {
        return new FirebaseLegacyPushNotificationService(firebaseServerUrl, httpProxyHost, httpProxyPort,authenticationJsonFile);
    }

    @Bean
    @Qualifier("healthMonitorService")
    public HealthMonitorService getMonitorService() {
    	return new HealthMonitorService(apiUserStatusCheckService());
    }
    @Bean
    public IJMXMonitorService apiUserStatusCheckService(){
    	return new APIUserStatusCheckService(fixAdapter());
    }

    @Bean
    @Qualifier("jmxFixAPIService")
    public JMXFixAPIService getFixSessionLogoutService() {
    	return new JMXFixAPIService(jmxFixSessionService());
    }

    @Bean
    public IJMXFixSessionService jmxFixSessionService(){
    	return new JMXFixSessionService(fixAdapter());
    }

    @Bean
    public IFixSequenceNumberResetService fixUserSequenceResetService(){
    	return new FixSequenceNumberResetService(fixAdapter());
    }

    private freemarker.template.Configuration templateConfiguration() {
        return new freemarker.template.Configuration(freemarker.template.Configuration.VERSION_2_3_28);
    }


    @Bean
    @Qualifier("findurPositionProvider")
    public IBackendPositionProvider positionProvider() {
    	return new FindurPositionProvider(findurWebserviceUser, findurWebservicePassword, findurWebserviceUrl);
	}

    @Bean
    public EmailClient emailClient(){
   	 return new EmailClient(smtpHost,smtpPort,emailEnvironment,emailFrom, tlsEnable, tlsVersion);
    }

    @Bean
    public CacheRefreshManager cacheRefreshManager(){
   	 return new CacheRefreshManager(cacheRefreshInstructionInterval,cachedConfiguration);
    }

    @Qualifier("bookingServiceImpl")
    @Bean
    AbstractBookingService bookingService() {
        if ( System.getProperty("simulateFindur") != null ) {
            return new SimulatedBookingService();
        } else {
            return new BookingServiceImpl(cachedConfiguration,new FindurFixClient(findurBookingConfig,emailClient(),bookingSupportEmail,cachedConfiguration.getApplicationId(),cachedConfiguration.isFindurOZBookingEnabled()),Platform.WTA);
        }
    }

    @Bean
    public MicrometerService micrometerService() {
        return new MicrometerService();
    }

    @Bean
    public IFindurAPIGatewayClient findurAPIGatewayClient() {
        return new FindurAPIGatewayClient(findurAPIGatewayBaseUrl, findurAPIGatewayAPIKey);
    }

    @EventListener(classes = { ContextRefreshedEvent.class })
	public void startAll(ContextRefreshedEvent event) throws Exception {
		if (event.getApplicationContext().equals(this.applicationContext)) {
			LOG.info("startAll ->");

			long start = System.currentTimeMillis();

			try {

				((AbstractWTA4Service) cachedConfiguration).startSync();

				micrometerService().startSync();

				activeMQLocalBroker().startSync();

				if (cachedConfiguration.isPushNotificationEnabled()) {
					((AbstractWTA4Service) pushNotificationService()).startSync();
				}

				InternalTradingAPIService lpInternalTradingAPI = lpInternalTradingAPI();
				LocalUnallocatedModuleImpl unallocatedModule = unallocatedModule();
				hookLFX2StatusListeners(lpInternalTradingAPI, unallocatedModule);

				((AbstractWTA4Service) lpInternalTradingAPI).startSync();

				unallocatedModule.startSync();

				fixAdapter().startSync();

				umGRPCAdapter().startSync();

				bookingService().startSync();

				cacheRefreshManager().startSync();

				((AbstractWTA4Service) commonAuthenticationProvider()).startSync();
				((AbstractWTA4Service) keycloakAuthenticationProvider()).startSync();
                ((AbstractWTA4Service) microsoftAuthenticationProvider()).startSync();

				persistenceManager.persistAuditLog(AuditLogBuilder.onApplicationStartAndStop(Boolean.TRUE,
						cachedConfiguration.getRootBusinessUnit(), cachedConfiguration.getSystemUser()));

			} catch (Exception e) {
				LOG.error("startAll - exception propagated", e);
				throw e;
			}

			double ellapsedSeconds = (System.currentTimeMillis() - start) / 1000d;
			LOG.info("startAll <- finished in {} seconds", ellapsedSeconds);
		}
	}

    private void hookLFX2StatusListeners(InternalTradingAPIService lpInternalTradingAPI, LocalUnallocatedModuleImpl unallocatedModule) {
        if ( lpInternalTradingAPI instanceof LFX2InternalTradingAPIImpl ) {
            ((LFX2InternalTradingAPIImpl)lpInternalTradingAPI).addLFX2LPStatusListener(unallocatedModule.getLPStatusController());
        } else if ( lpInternalTradingAPI instanceof ITASimulator ) {
            ((ITASimulator)lpInternalTradingAPI).setLfx2StatusListner(unallocatedModule.getLPStatusController());
        }
    }

    @EventListener(classes = { ContextClosedEvent.class })
    public void stopAll() throws Exception {
        LOG.info("stopAll ->");
        try {
            persistenceManager.persistAuditLog(AuditLogBuilder.onApplicationStartAndStop(Boolean.FALSE, cachedConfiguration.getRootBusinessUnit(), cachedConfiguration.getSystemUser()));
            ((AbstractWTA4Service) microsoftAuthenticationProvider()).stopSync();
            ((AbstractWTA4Service) keycloakAuthenticationProvider()).stopSync();
            ((AbstractWTA4Service)commonAuthenticationProvider()).stopSync();


            bookingService().stopSync();
            fixAdapter().stopSync();
            umGRPCAdapter().stopSync();
            unallocatedModule().stopSync();
            ((AbstractWTA4Service)lpInternalTradingAPI()).stopSync();
            cacheRefreshManager().stopSync();
            ((AbstractWTA4Service)cachedConfiguration).stopSync();
            ((AbstractWTA4Service)pushNotificationService()).stopSync();
            activeMQLocalBroker().stopSync();
            micrometerService().stopSync();
        } catch (Exception e) {
            LOG.error("stopAll - exception propagated", e);
            throw e;
        }

        LOG.info("stopAll <-");
    }

    protected boolean simulateLP() {
        if ( System.getProperty("simulateLP") != null ) {
            return true;
        } else {
            return false;
        }
    }

    private InternalTradingAPIService createLPSimulator() {
        ITASimulator simulator = new ITASimulator(TimeUnit.MILLISECONDS);
        simulator.configureSymbol("XAU/USD", itaSimulatorTicksPerSecond, new double[] { 100, 250, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 20000 }, 1800, 0.05, 2);
        simulator.configureSymbol("XAG/USD", itaSimulatorTicksPerSecond, new double[] { 10000, 25000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000, 110000, 120000, 130000, 140000, 150000, 160000, 1700000, 180000, 190000, 200000}, 24, 0.05, 4);
        simulator.configureSymbol("XPT/USD", itaSimulatorTicksPerSecond, new double[] { 1100, 250, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3250, 3500, 3750, 4000, 4250, 4500, 4750, 5000, 5250, 5500, 5750, 6000, 6250, 6500, 6750, 7000, 7250, 7500, 7750, 8000, 8250, 8500, 8750, 9000, 9250, 9500, 9750, 10000 }, 1000, 0.05, 2);
        simulator.configureSymbol("XPD/USD", itaSimulatorTicksPerSecond, new double[] { 100,150,200,250,300,350,400,450,500,550,600,650,700,750,800,850,900,950,1000,1050,1100,1150,1200,1250,1300,1350,1400,1450,1500,1550,1600,1650,1700,1750,1800,1850,1900,1950,2000,2100,2200,2300,2400,2500,2600,2700,2800,2900,3000,3100,3200,3300,3400,3500,3600,3700,3800,3900,4000,4100,4200,4300,4400,4500,4600,4700,4800,4900,5000 }, 2300, 0.05, 2);
        return simulator;
    }

}
