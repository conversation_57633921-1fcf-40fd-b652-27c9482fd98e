package ch.mks.wta4.um.exposure;

import ch.mks.wta4.common.metrics.MicrometerService;

public class FindurBusinessUnitPositionEngineMetrics {

    public void updateFindurResponseTime(String buId, long start) {
        MicrometerService.getDistributionSummary("wta4.positionengine.findur.response.time", "buId", buId)
            .record(now() - start);
    }

    public void updateFindurResponseProcessingTime(String buId, long start) {
        MicrometerService.getDistributionSummary("wta4.positionengine.findur.response.processing.time", "buId", buId)
            .record(now() - start);
    }

    public void incrementEventsBySource(String source) {
        MicrometerService.getCounter("wta4.positionengine.findur.events.by.source", "source", source)
            .increment();
    }

    public void incrementIgnoredEvents() {
        MicrometerService.getCounter("wta4.positionengine.findur.events.ignored")
            .increment();
    }

    private long now() {
        return System.currentTimeMillis();
    }
}
