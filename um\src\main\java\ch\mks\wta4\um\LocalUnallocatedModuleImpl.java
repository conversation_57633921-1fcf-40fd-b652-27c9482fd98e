package ch.mks.wta4.um;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.command.CommandRequest;
import ch.mks.wta4.command.CommandRequest.CommandType;
import ch.mks.wta4.command.CommandResponse;
import ch.mks.wta4.common.config.WTAEnvironmentConfiguration;
import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.IHolidayCalendar;
import ch.mks.wta4.configuration.model.Originator;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.Event;
import ch.mks.wta4.event.EventFactory;
import ch.mks.wta4.event.EventType;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.InternalTradingAPIService;
import ch.mks.wta4.ita.model.AuditLogBuilder;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.MarketDataSnapshot;
import ch.mks.wta4.ita.model.NewOrderRequest;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderUpdateRequest;
import ch.mks.wta4.ita.model.Privilege;
import ch.mks.wta4.ita.model.Quote;
import ch.mks.wta4.ita.model.QuoteCancel;
import ch.mks.wta4.ita.model.QuoteRequest;
import ch.mks.wta4.ita.model.QuoteRequestReject;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.Side;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.lfx2.LFX2LPStatusListener;
import ch.mks.wta4.limitcheck.ILimitCheckService;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;
import ch.mks.wta4.messaging.amq.ActiveMQPersistenceBus;
import ch.mks.wta4.notification.IEmailNotificationService;
import ch.mks.wta4.notification.push.IUmPushNotificationService;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceManager;
import ch.mks.wta4.um.autohedger.AutoHedger;
import ch.mks.wta4.um.autohedger.AutoHedgerPositionService;
import ch.mks.wta4.um.autohedger.HedgeProfileResolver;
import ch.mks.wta4.um.autohedger.IAutoHedgerPositionService;
import ch.mks.wta4.um.booking.AggregatedBookingEngine;
import ch.mks.wta4.um.dealercontrol.DealerController;
import ch.mks.wta4.um.event.CommandCenter;
import ch.mks.wta4.um.event.CommandRequestFactory;
import ch.mks.wta4.um.event.EventRouter;
import ch.mks.wta4.um.event.command.AcquirePrimaryStatusCommandHandler;
import ch.mks.wta4.um.event.command.BookAggregatedPositionCommandHandler;
import ch.mks.wta4.um.event.command.CloseAutoHedgerPositionCommandHandler;
import ch.mks.wta4.um.event.command.DestroySessionCommandHandler;
import ch.mks.wta4.um.event.command.GetAllAutoHedgerPositionsCommandHandler;
import ch.mks.wta4.um.event.command.GetAvailableStrategiesCommandHandler;
import ch.mks.wta4.um.event.command.GetBookingAggregatedPositionCommandHandler;
import ch.mks.wta4.um.event.command.GetOpenBookingAggregatedPositionsCommandHandler;
import ch.mks.wta4.um.event.command.GetOrdersFromAggregatedPositionCommandHandler;
import ch.mks.wta4.um.event.command.GetSessionsCommandHandler;
import ch.mks.wta4.um.event.command.PingCommandHandler;
import ch.mks.wta4.um.event.command.ReleasePrimaryStatusCommandHandler;
import ch.mks.wta4.um.event.command.ResetAutoHedgerPositionCommandHandler;
import ch.mks.wta4.um.event.command.SetActiveStrategyCommandHandler;
import ch.mks.wta4.um.event.handlers.NewDeviceUpdatedEventHandler;
import ch.mks.wta4.um.event.handlers.TradingEventHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.AggregatedBookingPositionUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.AggregatedPositionBookedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.ApplySpreadReductionFactorOnInternalizationUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUAutoHedgerStatusUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUCategoryUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUDistributionConfigurationDeletedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUDistributionConfigurationUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUExposureUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUForwardTradingCategoryUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUForwardTradingUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BUPVTDeletedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BasePriceComputationModeUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BookingAggregationInstructionCreatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BookingAggregationInstructionDeletedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BookingAggregationInstructionUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.BroadcastMessageHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.CPAuctionCommissionUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.CPHedgingModeUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.CPOfflineMarkupAndTypeUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.CPTradingStatusUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.CloseAutoHedgerPositionHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.DealRebookHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.ForwardCurveUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.InstanceHeartbeatHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.LPStatusUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.LiquidityProviderUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.LpSpreadFactorUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.MarketStatusUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.MinBidOfferSpreadUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.MinHedgingQuantityUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.OfflineMarketPriceDeletedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.OfflineMarketPriceUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.OverrideAuctionCommissionDeletedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.OverrideAuctionCommissionUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.OverridePriceVariationThresholdUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.PVTUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.ResetAutoHedgerPositionHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.SetActiveStrategyHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.SetAuctionPriceHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.SpreadReductionFactorOnInternalizationUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.SpreadUpdatedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.StalePriceDetectedHandler;
import ch.mks.wta4.um.event.handlers.dealercontroller.StaticPriceUpdatedHandler;
import ch.mks.wta4.um.exposure.BusinessUnitExposureEngine;
import ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine;
import ch.mks.wta4.um.exposure.IBusinessUnitPositionEngine;
import ch.mks.wta4.um.forward.ForwardInterestRateProvider;
import ch.mks.wta4.um.limitCheck.LimitCheckPositionProvider;
import ch.mks.wta4.um.limitCheck.WTA4LimitCheckService;
import ch.mks.wta4.um.lp.LFX2LPStatusController;
import ch.mks.wta4.um.lp.LiquidityProviderAdapter;
import ch.mks.wta4.um.notification.UMNotificationService;
import ch.mks.wta4.um.orchestrator.Orchestrator;
import ch.mks.wta4.um.orderengine.OrderEngine;
import ch.mks.wta4.um.orderengine.OrderRepository;
import ch.mks.wta4.um.orderengine.monitor.IOrderMonitorEngine.IOrderMonitorEngineListener;
import ch.mks.wta4.um.orderengine.monitor.OrderMonitorEngine;
import ch.mks.wta4.um.orderengine.validation.DuplicateClientOrderIdTracker;
import ch.mks.wta4.um.priceengine.PriceEngine;
import ch.mks.wta4.um.priceengine.PriceProvider;
import ch.mks.wta4.um.priceengine.mdsrepository.MDSRepository;
import ch.mks.wta4.um.privililegecheck.PrivilegeChecker;
import ch.mks.wta4.um.reports.ReportEngine;
import ch.mks.wta4.um.rfqengine.AutoRFQHandler;
import ch.mks.wta4.um.rfqengine.IRFQEngine;
import ch.mks.wta4.um.rfqengine.IRFQEngine.IRFQEngineListener;
import ch.mks.wta4.um.rfqengine.RFQEngine;
import ch.mks.wta4.um.rfqengine.RFQValidator;
import ch.mks.wta4.um.scheduler.SchedulerFactory;
import ch.mks.wta4.um.session.Session;
import ch.mks.wta4.um.session.SessionTracker;
import ch.mks.wta4.um.tradingstatus.MarketStatusController;

public class LocalUnallocatedModuleImpl extends AbstractWTA4Service implements IUnallocatedModule, IOrderMonitorEngineListener, IRFQEngineListener {

    private static final Logger LOG = LoggerFactory.getLogger(LocalUnallocatedModuleImpl.class);
    private final ICachedConfiguration configuration;
    private final SessionTracker sessionTracker;
    private final PriceEngine priceEngine;
    private final OrderEngine orderEngine;
    private final ActiveMQPersistenceBus persistenceQueueBus;
    private final IPersistenceManager synchPersistenceManager;
    private final AsynchPersistenceManager asynchPersistenceManager;
    private final AsynchPersistenceClient asynchPersistenceClient;
	private final IBookingService bookingService;
	private final LiquidityProviderAdapter liquidityProviderAdapter;
    private final MarketStatusController marketStatusController;
    private final LFX2LPStatusController lfx2lpStatusController;
    private final DealerController dealerController;
    private final OrderMonitorEngine orderMonitorEngine;
    private final OrderRepository orderRepository;
    private User systemUser;
    private final ILimitCheckService wta4LimitCheckService;
    private final LimitCheckPositionProvider limitCheckPositionProvider;
    private final ReportEngine reportEngine;
    private final SequentialIDGenerator sequentialIDGenerator;
    private final UMNotificationService umNotificationService;
    private final IRFQEngine rfqEngine;
    private final PriceProvider priceProvider;
    private final AutoHedger autoHedger;
    private final MDSRepository lpMDSRepository;
    private final PrivilegeChecker privilegeChecker;
    private final IQueryService queryService;
    private final HedgeProfileResolver hedgeProfileResolver;
    private final AggregatedBookingEngine aggregatedBookingEngine;

    private final FindurBusinessUnitPositionEngine findurBusinessUnitPositionEngine;
    private final BusinessUnitExposureEngine businessUnitExposureEngine;
    private final IFindurAPIGatewayClient findurAPIGatewayClient;

    private final EventRouter eventRouter;
    private final EventFactory eventFactory;
    private final Orchestrator orchestrator;
    private final CommandCenter commandCenter;
    private final CommandRequestFactory commandRequestFactory;
    private final ForwardInterestRateProvider forwardInterestRateProvider;
    private final DuplicateClientOrderIdTracker duplicateClientOrderIdTracker;
    private final TradingEventHandler tradingEventHandler;

    public LocalUnallocatedModuleImpl(
            ICachedConfiguration configuration,
            InternalTradingAPIService liquidityProviderITA,
            IPersistenceManager persistenceManager,
            IQueryService queryService,
            IBookingService bookingService,
            IEmailNotificationService emailNotificationService,
            IUmPushNotificationService umPushNotificationService,
            IFindurAPIGatewayClient findurAPIGatewayClient) {

        this.configuration = configuration;
        this.eventRouter = new EventRouter(configuration);
        this.eventFactory = new EventFactory(configuration.getInstanceInfo());
        this.orchestrator = new Orchestrator(eventRouter, configuration);
        
		this.bookingService = bookingService;
		this.queryService = queryService;
		this.synchPersistenceManager = persistenceManager;
		this.findurAPIGatewayClient = findurAPIGatewayClient;
        this.sequentialIDGenerator = new SequentialIDGenerator(configuration.getSequentialIDGeneratorStoreFilePath(), configuration.getApplicationZoneId(), configuration.getInstanceInfo().getUniqueId());
		this.orderRepository = new OrderRepository(queryService, configuration);
        this.persistenceQueueBus = new ActiveMQPersistenceBus(configuration.getAMQBrokerURL(), ActiveMQPersistenceBus.PERSISTENCE_QUEUE_BUS, configuration);
        this.asynchPersistenceManager = new AsynchPersistenceManager(persistenceQueueBus, persistenceManager,queryService);
        this.asynchPersistenceClient = new AsynchPersistenceClient(persistenceQueueBus);
        this.forwardInterestRateProvider = new ForwardInterestRateProvider(configuration);
        this.sessionTracker = new SessionTracker(configuration, forwardInterestRateProvider);
        this.liquidityProviderAdapter = new LiquidityProviderAdapter(configuration, liquidityProviderITA, asynchPersistenceClient, orderRepository, sequentialIDGenerator);
        this.marketStatusController = new MarketStatusController(configuration.getMarketStatusSchedule(),emailNotificationService, configuration, orchestrator, eventRouter);
        this.lfx2lpStatusController = new LFX2LPStatusController(configuration);

        this.lpMDSRepository = new MDSRepository(configuration.getMDSRepositoryStoreFile());
        this.priceProvider = new PriceProvider(marketStatusController, lpMDSRepository, persistenceManager, configuration, eventRouter, orchestrator);
        this.priceEngine = new PriceEngine(liquidityProviderAdapter, configuration, sessionTracker, marketStatusController, lpMDSRepository, priceProvider);

        this.limitCheckPositionProvider = new LimitCheckPositionProvider(configuration, priceProvider, queryService);
        this.wta4LimitCheckService = new WTA4LimitCheckService(configuration, priceProvider, limitCheckPositionProvider, emailNotificationService, sessionTracker );

        this.umNotificationService = new UMNotificationService(emailNotificationService, umPushNotificationService, sessionTracker, configuration);


        AutoRFQHandler autoRFQHandler = new AutoRFQHandler(priceProvider, configuration);
        this.rfqEngine = new RFQEngine(autoRFQHandler, this, new RFQValidator(configuration, sessionTracker), configuration);
        autoRFQHandler.setRFQEngine(rfqEngine);

        this.findurBusinessUnitPositionEngine = new FindurBusinessUnitPositionEngine(this.findurAPIGatewayClient, configuration);
        this.businessUnitExposureEngine = new BusinessUnitExposureEngine(findurBusinessUnitPositionEngine, priceProvider, configuration);

        this.duplicateClientOrderIdTracker = new DuplicateClientOrderIdTracker(queryService);

        this.commandCenter = new CommandCenter(configuration);
        this.commandRequestFactory = new CommandRequestFactory(configuration, this.orchestrator);
        
        this.orderMonitorEngine = new OrderMonitorEngine(this, orderRepository, configuration, marketStatusController, umNotificationService, orchestrator);
        aggregatedBookingEngine = new AggregatedBookingEngine(bookingService, queryService, sequentialIDGenerator, asynchPersistenceClient, persistenceManager, configuration, orderRepository, priceProvider, orchestrator);

        this.orderEngine = new OrderEngine(liquidityProviderAdapter, queryService, configuration, orderMonitorEngine, sessionTracker, asynchPersistenceClient, aggregatedBookingEngine, wta4LimitCheckService, marketStatusController, umNotificationService, orderRepository, sequentialIDGenerator, rfqEngine, priceProvider, eventRouter, duplicateClientOrderIdTracker, limitCheckPositionProvider);

        IAutoHedgerPositionService positionService = new AutoHedgerPositionService(queryService, persistenceManager);
        this.autoHedger = new AutoHedger(configuration.getSystemUser().getUserId(), configuration.getRootBusinessUnit().getBusinessUnitId(), this, positionService, configuration, asynchPersistenceClient, orderRepository, umNotificationService, orchestrator);

        this.dealerController = new DealerController(configuration, persistenceManager, sessionTracker, lfx2lpStatusController, orderEngine, marketStatusController, aggregatedBookingEngine, orderRepository, asynchPersistenceClient, queryService,wta4LimitCheckService, businessUnitExposureEngine, eventRouter, orchestrator, commandCenter, commandRequestFactory);
        priceEngine.setEventListener(dealerController);
        aggregatedBookingEngine.setAggregatedBookingPositionUpdatetListener(dealerController);
        businessUnitExposureEngine.addListener(dealerController);

        this.reportEngine = new ReportEngine(queryService, configuration, emailNotificationService, orchestrator);
        this.privilegeChecker = new PrivilegeChecker();

        this.hedgeProfileResolver = new HedgeProfileResolver(configuration);
        
        this.tradingEventHandler = new TradingEventHandler(sessionTracker, configuration.getRootBusinessUnit().getBusinessUnitId(), limitCheckPositionProvider, findurBusinessUnitPositionEngine, duplicateClientOrderIdTracker, orderRepository);
    }

    @Override
    public void startUp() throws Exception  {
        LOG.info("startUp->");
        try {
            systemUser = configuration.getSystemUser();
            eventRouter.startSync();
            initEventHandlers();
            orchestrator.startSync();
            commandCenter.startSync();
            initCommandHandlers();
            persistenceQueueBus.start();
            asynchPersistenceManager.start();
            sequentialIDGenerator.startSync();
            bookingService.registerListener(this.getOrderEngine());
            lfx2lpStatusController.addListener(this.dealerController);
            liquidityProviderAdapter.startSync();
            marketStatusController.startSync();
            lpMDSRepository.startSync();
            limitCheckPositionProvider.startSync();
            orderRepository.startSync();
            aggregatedBookingEngine.startSync();
            initExposureAndPostionEngines();
            orderEngine.startSync();
            initConfigurationPriceProvider(priceEngine);
            initMarketStatusControllerListeners();
            priceEngine.startSync();
            initOrderMonitorEngine();
            reportEngine.startSync();
            autoHedger.startSync();
        } catch (Throwable e) {
            LOG.error("startup -",e);
            throw new RuntimeException(e);
        }

        LOG.info("startUp <-");
    }

    private void initMarketStatusControllerListeners() {
       marketStatusController.addListener(priceEngine); // we want this one to be the first one, sends empty MDS and clears stuff
       marketStatusController.addListener(priceProvider); // computes the offline prices while going offline, if we do this first it delays the empty MDS
       // offline and online price streamers are registered later as part of the startup sequence of the priceEngine
    }

    private void initExposureAndPostionEngines() throws Exception {
        businessUnitExposureEngine.startSync();
        findurBusinessUnitPositionEngine.startSync();
    }

    private void initOrderMonitorEngine() throws Exception {
        orderMonitorEngine.registerListener(this);
        orderMonitorEngine.startSync();
    }

    private void initConfigurationPriceProvider(PriceEngine priceEngine) {
        configuration.setPriceProvider(currencyPairId -> priceProvider.getLastMDS(currencyPairId, true));
    }
    
    private void initEventHandlers() throws Exception {
        
            tradingEventHandler.startSync();
            
            eventRouter.registerHandler(EventType.CUSTOMER_EXECUTION_REPORT, tradingEventHandler);
            eventRouter.registerHandler(EventType.LP_EXECUTION_REPORT, tradingEventHandler);
            eventRouter.registerHandler(EventType.ORDER_CANCEL_REJECT, tradingEventHandler);
            eventRouter.registerHandler(EventType.BU_CATEGORY_UPDATED, new BUCategoryUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AGGREGATED_POSITION_BOOKED, new AggregatedPositionBookedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AGGREGATED_POSITION_UPDATED, new AggregatedBookingPositionUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AGGREGATION_INSTRUCTION_CREATED, new BookingAggregationInstructionCreatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AGGREGATION_INSTRUCTION_DELETED, new BookingAggregationInstructionDeletedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AGGREGATION_INSTRUCTION_UPDATED, new BookingAggregationInstructionUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.AUCTION_PRICE_SET, new SetAuctionPriceHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_AH_STATUS_UPDATED, new BUAutoHedgerStatusUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_CP_APPLY_SPREAD_REDUCTION_FACTOR_INT_UPDATED, new ApplySpreadReductionFactorOnInternalizationUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_CP_AUCTION_COMMISSION_OVERRIDE_DELETED, new OverrideAuctionCommissionDeletedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_CP_AUCTION_COMMISSION_OVERRIDE_UPDATED, new OverrideAuctionCommissionUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_DISTRIBUTION_CONFIG_DELETED, new BUDistributionConfigurationDeletedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_DISTRIBUTION_CONFIG_UPDATED, new BUDistributionConfigurationUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_EXPOSURE_UPDATED, new BUExposureUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_PVT_OVERRIDE_DELETED, new BUPVTDeletedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_PVT_OVERRIDE_UPDATED, new OverridePriceVariationThresholdUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_AUCTION_COMMISSION_UPDATED, new CPAuctionCommissionUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_BASE_COMPUTATION_MODE_UPDATED, new BasePriceComputationModeUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_CLOSE_AH_POSITION, new CloseAutoHedgerPositionHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_HEDGING_MODE_UPDATED, new CPHedgingModeUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_LP_SPREAD_FACTOR_UPDATED, new LpSpreadFactorUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_MIN_BID_OFFER_SPREAD_UPDATED, new MinBidOfferSpreadUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_MIN_HEDGING_QTY_UPDATED, new MinHedgingQuantityUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_OFFLINE_MARKUP_AND_TYPE_UPDATED, new CPOfflineMarkupAndTypeUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_OFFLINE_PRICE_UPDATED, new OfflineMarketPriceUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.STALE_PRICE_DETECTED, new StalePriceDetectedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_PVT_UPDATED, new PVTUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_RESET_AH_POSITION, new ResetAutoHedgerPositionHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_SET_ACTIVE_STRATEGY, new SetActiveStrategyHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_SPREAD_REDUCTION_FACTOR_INT_UPDATED, new SpreadReductionFactorOnInternalizationUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CP_STATIC_PRICE_UPDATED, new StaticPriceUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.CURRENCY_PAIR_TRADING_STATUS_SET, new CPTradingStatusUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.DEAL_REBOOKED, new DealRebookHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.LP_CONFIGURATION_UPDATED, new LiquidityProviderUpdatedHandler(sessionTracker, configuration, priceEngine));
            eventRouter.registerHandler(EventType.LP_STATUS_UPDATED, new LPStatusUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.MARKET_STATUS_UPDATED, new MarketStatusUpdatedHandler(sessionTracker, configuration, marketStatusController));
            eventRouter.registerHandler(EventType.MESSAGE_BROADCAST, new BroadcastMessageHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.SPREAD_UPDATED, new SpreadUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.INSTANCE_HEARTBEAT, new InstanceHeartbeatHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.FORWARD_CURVE_UPDATED, new ForwardCurveUpdatedHandler(sessionTracker, configuration, forwardInterestRateProvider));
            eventRouter.registerHandler(EventType.BU_FORWARD_TRADING_UPDATED, new BUForwardTradingUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.BU_FORWARD_TRADING_CATEGORY_UPDATED, new BUForwardTradingCategoryUpdatedHandler(sessionTracker, configuration));
            eventRouter.registerHandler(EventType.DEVICE_INFO_UPDATED, new NewDeviceUpdatedEventHandler(configuration));
            eventRouter.registerHandler(EventType.CP_OFFLINE_PRICE_DELETED, new OfflineMarketPriceDeletedHandler(sessionTracker, configuration));
    }

    private void initCommandHandlers() {
    	// TODO Autoregister command handlers?
		this.commandCenter.registerHandler(CommandType.PING, new PingCommandHandler(configuration));
		this.commandCenter.registerHandler(CommandType.GET_UM_SESSIONS, new GetSessionsCommandHandler(sessionTracker));
		this.commandCenter.registerHandler(CommandType.DESTROY_UM_SESSION, new DestroySessionCommandHandler(sessionTracker, asynchPersistenceClient, configuration, this));
		this.commandCenter.registerHandler(CommandType.ACQUIRE_PRIMARY_STATUS, new AcquirePrimaryStatusCommandHandler(orchestrator));
        this.commandCenter.registerHandler(CommandType.RELEASE_PRIMARY_STATUS, new ReleasePrimaryStatusCommandHandler(orchestrator));
		this.commandCenter.registerHandler(CommandType.GET_ALL_AUTO_HEDGER_POSITIONS, new GetAllAutoHedgerPositionsCommandHandler(queryService));
		this.commandCenter.registerHandler(CommandType.GET_AVAILABLE_STRATEGIES, new GetAvailableStrategiesCommandHandler(autoHedger));
		this.commandCenter.registerHandler(CommandType.CLOSE_AUTO_HEDGER_POSITION, new CloseAutoHedgerPositionCommandHandler(asynchPersistenceClient, configuration, autoHedger, eventRouter));
		this.commandCenter.registerHandler(CommandType.RESET_AUTO_HEDGER_POSITION, new ResetAutoHedgerPositionCommandHandler(asynchPersistenceClient, configuration, autoHedger, eventRouter));
		this.commandCenter.registerHandler(CommandType.SET_ACTIVE_STRATEGY, new SetActiveStrategyCommandHandler(asynchPersistenceClient, configuration, autoHedger, eventRouter));
		this.commandCenter.registerHandler(CommandType.GET_OPEN_BOOKING_AGGREGATED_POSITIONS, new GetOpenBookingAggregatedPositionsCommandHandler(aggregatedBookingEngine));
		this.commandCenter.registerHandler(CommandType.BOOK_AGGREGATED_POSITION, new BookAggregatedPositionCommandHandler(configuration, aggregatedBookingEngine, asynchPersistenceClient, eventRouter));
		this.commandCenter.registerHandler(CommandType.GET_BOOKING_AGGREGATED_POSITION, new GetBookingAggregatedPositionCommandHandler(aggregatedBookingEngine));
		this.commandCenter.registerHandler(CommandType.GET_ORDERS_FROM_AGGREGATED_POSITION, new GetOrdersFromAggregatedPositionCommandHandler(queryService));
	}

    @Override
    public void shutDown() throws Exception {
        LOG.info("shutDown->");
        autoHedger.stopSync();
        orderMonitorEngine.stopSync();
        priceEngine.stopSync();
        orderEngine.stopSync();
        findurBusinessUnitPositionEngine.stopSync();
        businessUnitExposureEngine.stopSync();
        aggregatedBookingEngine.stopSync();
        orderRepository.stopSync();
        limitCheckPositionProvider.stopSync();
        lpMDSRepository.stopSync();
        marketStatusController.stopSync();
        liquidityProviderAdapter.stopSync();
        sequentialIDGenerator.stopSync();
        asynchPersistenceManager.stop();
        persistenceQueueBus.stop();
        SchedulerFactory.stopAll();
        commandCenter.stopSync();
        orchestrator.stopSync();
        tradingEventHandler.stopSync();
        eventRouter.stopSync();
        LOG.info("shutDown<-");
    }

    @Override
    public void marketDataRequest(String requestId, String currencyPairId, Tenor tenor, LocalDate valueDate, String sessionId) {
        try {
            LOG.info("marketDataRequest -> requestId={}, currencyPairId={}, tenor={}, valueDate={}, sessionId={}", requestId, currencyPairId, tenor, valueDate, sessionId);
            assertSessionIsActive(sessionId);
            priceEngine.marketDataRequest(requestId, currencyPairId, tenor, valueDate, sessionId);
            LOG.info("marketDataRequest <-");
        } catch (Exception e) {
            LOG.error("marketDataRequest - requestId={}, currencyPairId={}, tenor={}, valueDate={}, sessionId={}", requestId, currencyPairId, tenor, valueDate, sessionId, e);
            throw new RuntimeException(e);
        }
    }
    
    @Override
    public void marketDataCancelRequest(String requestId, String currencyPairId, String sessionId) {
        try {
            LOG.info("marketDataCancelRequest -> requestId={}, currencyPairId={}, sessionId={}", requestId, currencyPairId, sessionId);
            assertSessionIsActive(sessionId);
            priceEngine.marketDataRequestCancel(requestId, currencyPairId, sessionId);
            LOG.info("marketDataCancelRequest <- ");
        } catch (Exception e) {
            LOG.error("marketDataCancelRequest - requestId={}, currencyPairId={}, sessionId={}", requestId, currencyPairId, sessionId,e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void placeOrder(NewOrderRequest newOrderRequest) {
        try {
            LOG.info("placeOrder -> newOrderRequest={}", newOrderRequest);
            assertSessionIsActive(newOrderRequest.getSessionId());
            Order order = getOrderFromNewOrderRequest(newOrderRequest);
            orderRepository.add(order);
            orderEngine.onNewOrderRequest(order, getUserFromSessionId(newOrderRequest.getSessionId()));
            LOG.info("placeOrder <- newOrderRequest={}, order={}", newOrderRequest, order);
        } catch (Exception e) {
            LOG.error("placeOrder - newOrderRequest={}", newOrderRequest, e);
            throw new RuntimeException(e);
        }
    }

    //FIXME this logic is too convoluted because the same function (placeOrder) is used to manage
    // (1) customer orders (orders that customers place with MKS) and
    // (2) hedging orders (orders that dealers place on behalf of MKS to the LPs)
    // a better approach is to create a new API method to placeHedgingOrder, called by the dealer UI and  AH for hedging orders.
    private Order getOrderFromNewOrderRequest(NewOrderRequest newOrderRequest) {
        Order order = new Order(newOrderRequest);

        /*
         * UM convention is all orders operations are from the system point of view If
         * order is from customer and customer wants to sell, system is buying => BUY
         * order, If order is from customer and customer wants to buy, system is selling
         * => SELL order If order is from dealer operation is not changed
         */
        
        if ( order.getType() == OrderType.FOK ) {
            
            resolveMarketDataSnapshot(order);
            resolveForwardFOKTenorAndValueDate(order);    
            
        } else if ( order.getType() == OrderType.MARKET) {
            
            order.setExecutionMarketDataSnapshot(getLastMarketDataSnapshot(order.getSessionId(), order.getCurrencyPairId(), order.getTenor(), order.getValueDate()));
            
        } else if ( order.getType() == OrderType.DEAL_TICKET ) {
            
            if ( order.isForwardOrder() ) {
                resolveForwardDealTicketValueDate(order);
            }
            
        }

        Session session = sessionTracker.getSession(newOrderRequest.getSessionId());
        String hegingBUId = configuration.getHedgingBusinessUnit().getBusinessUnitId();

        if ( session.isDealerSession() && hegingBUId.equals(newOrderRequest.getBuId()) ) {

            order.setOriginator(Originator.DEALER);
            order.setHedgingMode(HedgingMode.NA);

        } else {

            order.setOriginator(Originator.CUSTOMER);
            order.setOperation(order.getOperation().getReverseOperation()); // this has to go BEFORE the setting the hedging mode, as the operation info is relevant there
            order.setHedgingMode(resolveHedgingMode(order));

        }

        return order;
    }

    private void resolveForwardFOKTenorAndValueDate(Order order) {
        MarketDataSnapshot mds = order.getExecutionMarketDataSnapshot();
        if ( mds != null ) {
            LOG.info("resolveMarketDataSnapshot - setting up order with tenor={} and valueDate={}. mds={}", mds.getTenor(), mds.getValueDate(), mds);
            order.setTenor(mds.getTenor());
            order.setValueDate(mds.getValueDate());
        }
    }

    private void resolveForwardDealTicketValueDate(Order order) {
        Tenor tenor = order.getTenor();
        
        if ( tenor == null || tenor == Tenor.SPOT || tenor == Tenor.BROKEN ) return; //use what comes with the request
        
        IHolidayCalendar holidayCalendar = configuration.getHolidayCalendar();
        String currencyPairId = order.getCurrencyPairId();
        LocalDate tradeDate = holidayCalendar.getTradeDate(currencyPairId);
        LocalDate valueDate = holidayCalendar.computeValueDate(tradeDate, tenor, currencyPairId);
        
        order.setValueDate( valueDate );
    }

    @Override
    public void updateOrder(OrderUpdateRequest orderUpdateRequest) {
        try {
            LOG.info("updateOrder -> orderUpdateRequest={}", orderUpdateRequest);

            assertSessionIsActive(orderUpdateRequest.getSessionId());

            Order order = getOrderAndCheckConsistency(orderUpdateRequest.getOrderId(), orderUpdateRequest.getSessionId());
            if ( order == null ) {
                orderEngine.rejectUpdate(null, orderUpdateRequest, "Order not found");
                LOG.info("updateOrder <- order not found");
                return;
            }

            if ( order.getOriginator() == Originator.CUSTOMER ) {
                orderUpdateRequest.setOperation(orderUpdateRequest.getOperation().getReverseOperation());
            }

            orderEngine.onUpdateOrderRequest(order, orderUpdateRequest, getUserFromSessionId(orderUpdateRequest.getSessionId()));
            LOG.info("updateOrder <-");
        } catch (Exception e) {
            LOG.error("updateOrder - orderUpdateRequest={}", orderUpdateRequest, e);
            throw new RuntimeException(e);
        }
    }


    private Order getOrderAndCheckConsistency(String orderId, String sessionId) {

        Order order = orderRepository.getOrderByOrderId(orderId);

        if ( order == null ) {
            LOG.warn("getOrderAndCheckConsistency - Order not found orderId={}, sessionId={}", orderId, sessionId);
            return null;
        }

        Session session =  sessionTracker.getSession(sessionId);
        if ( session.isCustomerSession() ) {
            String sessionBuId =  session.getBuId();
            String orderBuId = order.getBuId();
            if ( ! order.getBuId().equals(sessionBuId) ) {
                LOG.warn("getOrderAndCheckConsistency - Order does not belong to session BU. sessionBuId={}, orderBuId={}, order={}", sessionBuId, orderBuId, order);
                return null;
            }
        }
        return order;
    }

    private void resolveMarketDataSnapshot(Order customerOrder) {
        MarketDataSnapshot mds = null;
        if ( customerOrder.getQuoteId() != null ) {
            mds = getMarketDataSnapshot(customerOrder.getSessionId(), customerOrder.getCurrencyPairId(), customerOrder.getQuoteId(), customerOrder.getTenor(), customerOrder.getValueDate());
        } else {
            // required for FOK from ST FIX API!
            LOG.info("resolveMarketDataSnapshot - quoteId is null, resolving by price and qty. customerOrder={}", customerOrder);
            mds = getMarketDataSnapshotByPriceAndQty(customerOrder.getSessionId(), customerOrder.getCurrencyPairId(), customerOrder.getOperation(), customerOrder.getLimitPrice(), customerOrder.getBaseQuantity());
            if ( mds != null ) {
                customerOrder.setQuoteId(mds.getId());
            }
        }
        
        if ( mds == null ) {
            LOG.info("resolveMarketDataSnapshot - could not find quotetId for customerOrder={}", customerOrder);
            return;
        } else {
            customerOrder.setExecutionMarketDataSnapshot(mds);
            long quoteDelay = customerOrder.getCreatedTimestamp() - mds.getCreationTimeStamp();
            customerOrder.setQuoteDelay(quoteDelay);
            LOG.info("resolveMarketDataSnapshot - clientOrderId={}, mdsId={}, quoteDelay={}", customerOrder.getClientOrderId(), mds.getId(), quoteDelay);
        }
    }

    private MarketDataSnapshot getMarketDataSnapshotByPriceAndQty(String sessionId, String currencyPairId, Operation operation, Double limitPrice, Double qty) {
        Session session = sessionTracker.getSession(sessionId);
        // order operation already reversed at this stage, Operation.BUY -> customer sells on BID
        Side side = operation == Operation.BUY?Side.BID:Side.OFFER;
        return session.getMarketDataSnapshotByPriceAndQuantity(currencyPairId, side, limitPrice, qty);
    }

    private MarketDataSnapshot getMarketDataSnapshot(String sessionId, String currencyPairId, String quoteId, Tenor tenor, LocalDate valueDate) {
        Session session = sessionTracker.getSession(sessionId);
        MarketDataSnapshot mds = session.getMarketDataSnapshotIfPresent(currencyPairId, quoteId, tenor, valueDate);
        if ( mds == null ) {
            LOG.info("getMarketDataSnapshot - could not find quotetId {} with currencyPairId={}, tenor={}, valueDate={} in session={}", quoteId, currencyPairId, tenor, valueDate, session);
        }
        return mds;
    }

    private MarketDataSnapshot getLastMarketDataSnapshot(String sessionId, String currencyPairId, Tenor tenor, LocalDate valueDate) {
        Session session = sessionTracker.getSession(sessionId);
        return session.getLastSentMarketDataSnapshot(currencyPairId, tenor, valueDate);
    }

    private HedgingMode resolveHedgingMode(Order order) {
        
        if ( order.isForwardOrder() ) {
            LOG.info("resolveHedgingMode - forced to MANUAL because order is not for SPOT, order={}", order);
            return HedgingMode.MANUAL;
        }
        
        HedgingMode hedgingMode = hedgeProfileResolver.resolveHedgingMode(order.getCurrencyPairId(),  order.getType(),  order.getOperation(), order.getBuId());

        if ( hedgingMode == HedgingMode.AUTO && ! marketStatusController.isMarketOnline() ) {
            LOG.info("resolveHedgingMode - forced to MANUAL because market is not online, order={}", order);
            hedgingMode = HedgingMode.MANUAL;
        }

        return hedgingMode;
    }

    @Override
    public void cancelOrder(OrderCancelRequest orderCancelRequest) {
        try {
            LOG.info("cancelOrder -> orderCancelRequest={}", orderCancelRequest);
            assertSessionIsActive(orderCancelRequest.getSessionId());
            Order order = getOrderAndCheckConsistency(orderCancelRequest.getOrderId(), orderCancelRequest.getSessionId());
            if ( order == null ) {
                orderEngine.rejectCancel(null, orderCancelRequest, "Order not found");
                LOG.info("cancelOrder <- order not found");
                return;
            }
            orderEngine.onCancelOrderRequest(order, orderCancelRequest, configuration.getUser(order.getUserId()));
            LOG.info("cancelOrder <- ");
        } catch (Exception e) {
            LOG.error("cancelOrder - orderCancelRequest={}", orderCancelRequest, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String createSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, Channel channel) {
        try {
            LOG.info("createSession -> userId={}, buId={}, channel={}", userId, buId, channel);

            assertUserBUConsistency(userId, buId);

            String sessionId = sessionTracker.createSession(userId, buId, pricingListener, tradingListener, adminListener, channel);
            limitCheckPositionProvider.onSessionCreated(buId);
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onUMSessionCreationAndDestroy(configuration.getBusinessUnit(buId), configuration.getUser(userId), sessionId, Boolean.TRUE));
            LOG.info("createSession <- session={} for userId={} and buId={}", sessionId, userId, buId);
            return sessionId;
        } catch (Exception e) {
            LOG.error("createSession -userId={}, buId={}, channel={}", userId, buId, channel, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String createSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, Channel channel) {
        return createSession(userId, buId, pricingListener, tradingListener, null, channel);
    }

    private void assertUserBUConsistency(String userId, String buId) {
        User user = configuration.getUser(userId);
        if ( user != null ) {
            if ( user.getBusinessUnitUserProfiles().containsKey(buId) ) {
                return; //It's all good
            } else {
                LOG.error("assertUserBUConsistency - userId={}, buId={} not consistent", userId, buId);
            }

        } else {
            LOG.error("assertUserBUConsistency - userId={}, buId={} user not found", userId, buId);
        }
        throw new RuntimeException(String.format("assertUserBUConsistency - userId=%s, buId=%s not consistent", userId, buId));
    }

    @Override
    public void forceDestroySession(String sessionId, String sessionToDestroyId, String regionId, String instanceId) {
        LOG.info("forceDestroySession -> sessionId={}, sessionToDestroyId={}, regionId={}, instanceId={}", sessionId, sessionToDestroyId, regionId, instanceId);
        assertSessionIsActive(sessionId);
        String dealerUserId = getDealerUserFromSessionId(sessionId).getUserId();
        final InstanceInfo instanceInfo = new InstanceInfo(
              WTAEnvironmentConfiguration.getAppId(),
              Optional.ofNullable(regionId).orElse(WTAEnvironmentConfiguration.getRegionId()),
              Optional.ofNullable(instanceId).orElse(WTAEnvironmentConfiguration.getInstanceId()));

        CommandRequest request = this.commandRequestFactory.createForceDestroySessionCommandRequest(instanceInfo, sessionToDestroyId, dealerUserId);
		CommandResponse response = this.commandCenter.sendCommandRequest(request);
        if (response.isError()) {
            LOG.error("forceDestroySession - sessionId={}, sessionToDestroyId={}, regionId={}, instanceId={}: {}", sessionId, sessionToDestroyId, regionId, instanceId, response.getErrorMessage());
        	throw new RuntimeException(response.getErrorMessage());
        }
        
        LOG.info("forceDestroySession <- response={}", response);
    }

    @Override
    public void destroySession(String sessionId) {
        try {
            LOG.info("destroySession -> sessionId={}", sessionId);
            assertSessionIsActive(sessionId);
            Session session = sessionTracker.getSession(sessionId);
            if ( session != null ) {
                priceEngine.destroySession(sessionId);
                sessionTracker.destroySession(sessionId);
                asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onUMSessionCreationAndDestroy(configuration.getBusinessUnit(session.getBuId()), configuration.getUser(session.getUserId()), sessionId, Boolean.FALSE));
            } else {
                LOG.info("destroySession - sessionId={} not found", sessionId);
            }
            LOG.info("destroySession <-");
        } catch (Exception e) {
            LOG.error("destroySession - sessionId={}", sessionId, e);
            throw new RuntimeException(e.getMessage());
        }
    }
    
    public OrderEngine getOrderEngine() {
        return orderEngine;
    }

    public LFX2LPStatusListener getLPStatusController() {
        return lfx2lpStatusController;
    }

    /** Dealer controller API, forward all to dealer controller  **/
    @Override
    public void trigger(String sessionId, String orderId) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_TRIGGER_ORDERBOOK);
    	dealerController.trigger(getDealerUserFromSessionId(sessionId), orderId);
    }

    @Override public void cancel(String sessionId, String orderId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_CANCEL_ORDERBOOK);
        dealerController.cancel(getDealerUserFromSessionId(sessionId), orderId, sessionId);
    }

    @Override
    public void execute(String sessionId, String orderId, Double executionPrice) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_EXECUTE_ORDERBOOK);
    	dealerController.execute(getDealerUserFromSessionId(sessionId), orderId, executionPrice);
    }
    @Override
    public void reactivate(String sessionId, String orderId) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_TRIGGER_ORDERBOOK);
    	dealerController.reactivate(getDealerUserFromSessionId(sessionId), orderId);
    }
    @Override
    public void setAuctionPrice(String sessionId, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUCTION_PRICE);
    	dealerController.setAcutionPrice(getDealerUserFromSessionId(sessionId), currencyPairId, auctionSessionTime, auctionPrice);
    }
    @Override
    public void updateLiquidityProvider(String sessionId, String lpId, boolean enabledForTrading, boolean enabledForPricing) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_LP_UPDATE);
    	dealerController.updateLiquidityProvider(getDealerUserFromSessionId(sessionId), lpId, enabledForTrading, enabledForPricing);
    }
    @Override
    public String createDealerSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener, Channel channel) {
    	return dealerController.createDealerSession(userId, buId, pricingListener, tradingListener, adminListener, dealerControlListener, channel);
    }
    @Override
    public void updateSpread(String sessionId, String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset,SpreadType spreadType) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_SPREAD);
    	dealerController.updateSpread(getDealerUserFromSessionId(sessionId), categoryId, currencyPairId, band, bidOffset, offerOffset,spreadType);
    }
	@Override
	public void updateBusinessUnitCategory(String sessionId, String categoryId, String buId) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_SPREAD);
		dealerController.updateBusinessUnitCategory(getDealerUserFromSessionId(sessionId), categoryId, buId);
	}
	@Override
	public void updateMinimumBidOfferSpread(String sessionId, String currencyPairId, Double spread) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_MBOS);
		dealerController.updateMinimumBidOfferSpread(getDealerUserFromSessionId(sessionId), currencyPairId, spread);
	}
	@Override
	public void updateMinimumHedgingQuantity(String sessionId, String currencyPairId, Double minimumHedgingQuantity) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_MIN_HEDGE_QTY);
		dealerController.updateMinimumHedgingQuantity(getDealerUserFromSessionId(sessionId), currencyPairId, minimumHedgingQuantity);
	}
	@Override
	public void updateCurrencyPairTradingStatus(String sessionId, List<String> currencyPairIds,TradingStatus tradingStatus) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_TRADING_STATUS);
		dealerController.updateCurrencyPairTradingStatus(getDealerUserFromSessionId(sessionId), currencyPairIds, tradingStatus);
	}
    @Override public LPStatusUpdate getLPStatus() {return dealerController.getLPStatus();}
    @Override
    public void updateCurrencyPairHedgingMode(String sessionId, String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_TRADING_STATUS);
    	dealerController.updateCurrencyPairHedgingMode(getDealerUserFromSessionId(sessionId), currencyPairId, orderType, hedgingMode, hedgingOperation);
    }
    @Override public MarketStatus getMarketStatus() {return dealerController.getMarketStatus();}
    @Override
    public MarketStatus setMarketStatus(String sessionId, MarketStatus marketStatus) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_MARKET_STATUS);
    	return dealerController.setMarketStatus(getDealerUserFromSessionId(sessionId), marketStatus);
    }
	@Override
	public void rebookOrder(String orderId,String  sessionId) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_REBOOK);
		dealerController.rebookOrder(getDealerUserFromSessionId(sessionId), orderId);
	}


	@Override
	public void closeAutoHedgerPosition(String sessionId, String currencyPairId) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUTO_HEDGER);
		dealerController.closeAutoHedgerPosition(getDealerUserFromSessionId(sessionId), currencyPairId);
	}
	@Override
	public void resetAutoHedgerPosition(String sessionId, String currencyPairId) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUTO_HEDGER);
		dealerController.resetAutoHedgerPosition(getDealerUserFromSessionId(sessionId), currencyPairId);
	}
	@Override public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId) {return dealerController.getAvailableStrategies(currencyPairId);}
    @Override
    public void setActiveStrategy(String sessionId, String currencyPairId, String strategyId) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUTO_HEDGER);
    	dealerController.setActiveStrategy( getDealerUserFromSessionId(sessionId), currencyPairId, strategyId);
    }


	@Override public List<Order> getAllActiveRestingOrders(String sessionId) {
        try{
            LOG.info("getAllActiveRestingOrder -> sessionId={}", sessionId);
            List<Order> orders = orderRepository.getActiveRestingOrders();
            LOG.info("getAllActiveRestingOrder <- returning {} orders", orders.size());
            return orders;
        } catch(Exception e){
            LOG.error("getAllActiveRestingOrder -", e);
            throw new RuntimeException("getAllActiveRestingOrder -", e);
        }
    }

    @Override
    public void updatePVT(String sessionId, String currencyPairId, Double pvt) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_PVT);
    	dealerController.updatePVT(getDealerUserFromSessionId(sessionId), currencyPairId, pvt);
    }
    @Override
    public void updateBusinessUnitAutoHedgerStatus(String sessionId, boolean autoHedgerStatus, String buId) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_SPREAD);
    	dealerController.updateBusinessUnitAutoHedgerStatus(getDealerUserFromSessionId(sessionId), autoHedgerStatus, buId);
    }
    @Override public void broadcastMessage(String sessionId, SessionMessage message) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_SEND_BROADCAST_MESSAGE);
    	dealerController.broadcastMessage(getDealerUserFromSessionId(sessionId), message);
    }
    @Override
    public void updateCurrencyPairAuctionCommission(String sessionId, String currencyPairId, Double bidCommission,Double offerCommission) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUCTION_COMMISSION);
    	dealerController.updateCurrencyPairAuctionCommission(getDealerUserFromSessionId(sessionId), currencyPairId, bidCommission,offerCommission);
    }
    @Override public void updateStaticPrice(String sessionId, StaticPrice staticPrice) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_STATIC_PRICE);
    	dealerController.updateStaticPrice(getDealerUserFromSessionId(sessionId), staticPrice) ;
    }

    @Override
    public void updateOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId, Double bidCommission, Double offerCommission) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUCTION_COMMISSION);
    	dealerController.updateOverrideAuctionCommission(
                getDealerUserFromSessionId(sessionId),
                businessUnitId,
                currencyPairId,
                bidCommission,
                offerCommission
        );
    }

    @Override
    public void deleteOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_AUCTION_COMMISSION);
        dealerController.deleteOverrideAuctionCommission(
                getDealerUserFromSessionId(sessionId),
                businessUnitId,
                currencyPairId
        );
    }

    @Override
    public void updateOfflineMarketPrice(String sessionId, String currencyPairId, Double bid, Double offer) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_OFFLINE);
    	dealerController.updateOfflineMarketPrice(getDealerUserFromSessionId(sessionId), currencyPairId, bid, offer);
    }
    @Override
    public void updateCurrencyPairOfflineMarkupAndType(String sessionId, String currencyPairId, Double offlineMarkup,OfflineMarkupType markUpType) {
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_OFFLINE);
    	dealerController.updateCurrencyPairOfflineMarkupAndType(getDealerUserFromSessionId(sessionId), currencyPairId, offlineMarkup,markUpType);
    }

    @Override
    public void updateBasePriceComputationMode(String sessionId, String currencyPairId, BasePriceComputationMode basePriceComputationMode) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BASE_PRICE_COMPUTATION_MODE);
        dealerController.updateBasePriceComputationMode(getDealerUserFromSessionId(sessionId), currencyPairId, basePriceComputationMode);
    }

    @Override
    public void updateLpSpreadFactor(String sessionId, String currencyPairId, Double lpSpreadFactor) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_LP_SPREAD_FACTOR);
        dealerController.updateLpSpreadFactor(getDealerUserFromSessionId(sessionId), currencyPairId, lpSpreadFactor);
    }

    @Override
    public void updateSpreadReductionFactorOnInternalization(String sessionId, String currencyPairId, Double spreadReductionFactorOnInternalization) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION);
        dealerController.updateSpreadReductionFactorOnInternalization(getDealerUserFromSessionId(sessionId), currencyPairId, spreadReductionFactorOnInternalization);
    }

    @Override
    public void updateApplySpreadReductionFactorOnInternalization(String sessionId, String businessUnitId, boolean applySpreadReductionFactorOnInternalization) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_APPLY_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION);
        dealerController.updateApplySpreadReductionFactorOnInternalization(getDealerUserFromSessionId(sessionId), businessUnitId, applySpreadReductionFactorOnInternalization);
    }


    @Override
    public Collection<SessionInfo> getActiveSessions(String sessionId) {
        try {
            LOG.info("getActiveSessions -> sessionId={}", sessionId);
            Collection<SessionInfo> sessions = this.dealerController.getActiveSessions();
            LOG.info("getActiveSessions <- returning {} sessions", sessions);
            return sessions;
        } catch (Exception e) {
            LOG.error("getActiveSessions - sessionId={}", sessionId, e);
            throw new RuntimeException("sessionId=" + sessionId, e);
        }
    }

    @Override
    public SessionInfo getSession(String sessionId) {
    	SessionInfo sessionInfo = null;
    	 try {
             LOG.debug("getSession -> sessionId={}", sessionId);
             Session session = sessionTracker.getSession(sessionId);
             if(null != session) {
            	 sessionInfo = new SessionInfo(session.getSessionId(), session.getUserId(), session.getBuId(), session.getChannel(), session.getSessionType(), session.getSessionCreationTime(), configuration.getInstanceInfo().getRegionId(), configuration.getInstanceInfo().getInstanceId());
             }
             LOG.debug("getSession <- sessionInfo={} ", sessionInfo);
             return sessionInfo;
         } catch (Exception e) {
             LOG.error("getSession - sessionId={}", sessionId, e);
             throw new RuntimeException("sessionId=" + sessionId, e);
         }
    }

    /**
     * Order monitor listener implementation
     */

    @Override
    public void onMonitorEngineOrderTriggered(String orderId) {
        try {
            LOG.info("onMonitorEngineOrderTriggered -> orderId={}", orderId);
            dealerController.trigger(systemUser, orderId);
            LOG.info("onMonitorEngineOrderTriggered <-");
        } catch (Exception e) {
            LOG.error("onMonitorEngineOrderTriggered - orderId={}", orderId, e);
        }
    }

    @Override
    public void onMonitorEngineOrderCancelled(String orderId) {
        try {
            LOG.info("onMonitorEngineOrderCancelled -> orderId={}", orderId);
            LOG.info("onMonitorEngineOrderCancelled <-");
        } catch (Exception e) {
            LOG.error("onMonitorEngineOrderCancelled - orderId={}", orderId, e);
        }
    }

    @Override
    public void onMonitorEngineOrderRejected(String orderId, String rejectReason) {
        try {
            LOG.info("onMonitorEngineOrderRejected -> orderId={}, rejectReason={}", orderId, rejectReason);
            LOG.info("onMonitorEngineOrderRejected <-");
        } catch (Exception e) {
            LOG.error("onMonitorEngineOrderRejected - orderId={}, rejectReason={}", orderId, rejectReason, e);
        }
    }

    @Override
    public void onMonitorEngineOrderExpired(String orderId) {
        try {
            LOG.info("onMonitorEngineOrderExpired -> orderId={}", orderId);
            dealerController.expire(systemUser, orderId);
            LOG.info("onMonitorEngineOrderExpired <-");
        } catch (Exception e) {
            LOG.error("onMonitorEngineOrderExpired - orderId={}", orderId, e);
        }
    }
    /**
     * END Order monitor listener implementation
     */


    private User getDealerUserFromSessionId(String sessionId) {
        try {
            Objects.requireNonNull(sessionId);
            Session session = sessionTracker.getSession(sessionId);

            if ( session != null && session.isDealerSession() ) {
                String userId = session.getUserId();
                return configuration.getUser(userId);
            } else {
                LOG.error("Session id={} [{}] is not a dealer session", sessionId, session);
                throw new RuntimeException(String.format("Session id=%s [%s] is not a dealer session", sessionId, session));
            }
        } catch (Exception e) {
            LOG.error("getUserFromSessionId - sessionId={}", sessionId, e);
            throw new RuntimeException(e);
        }
    }

    private User getUserFromSessionId(String sessionId) {
        LOG.info("getUserFromSessionId -> sessionId={}", sessionId);
        String userId = sessionTracker.getSession(sessionId).getUserId();
        User user = configuration.getUser(userId);
        LOG.info("getUserFromSessionId <- user-{}", user);
        return user;
    }

	@Override
	public LimitCheckReport checkLimit(String sessionId,Order order) {
		LOG.info("checkLimit -> sessionId={},order={}",sessionId, order);
		LimitCheckReport limitCheckReport = wta4LimitCheckService.checkLimits(order);
		LOG.info("checkLimit <- limitCheckReport={}",limitCheckReport);
		return limitCheckReport;
	}

	/** RFQ stuff **/
    @Override
    public void rfq(QuoteRequest quoteRequest) {
        try {
            LOG.info("rfq -> quoteRequest={}", quoteRequest);
            rfqEngine.requestForQuote(quoteRequest);
            LOG.info("rfq <-");
        } catch (Exception e) {
            LOG.error("rfq - quoteRequest={}", quoteRequest, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onQuote(Quote quote) {
        try {
            LOG.info("onQuote -> quote={}", quote);
            Session session = sessionTracker.getSession(quote.getQuoteRequest().getSessionId());
            if ( session.getTradingListener() != null ) {
                session.getTradingListener().onQuote(quote);
            }
            LOG.info("onQuote <-");
        } catch (Exception e) {
            LOG.error("onQuote - quote={}", quote, e);
        }
    }

    @Override
    public void onQuoteRequestReject(QuoteRequestReject quoteRequestReject) {
        try {
            LOG.info("onQuoteRequestReject -> quoteRequestReject={}", quoteRequestReject);
            Session session = sessionTracker.getSession(quoteRequestReject.getQuoteRequest().getSessionId());
            if ( session.getTradingListener() != null ) {
                session.getTradingListener().onQuoteRequestReject(quoteRequestReject);
            }
            LOG.info("onQuoteRequestReject <-");
        } catch (Exception e) {
            LOG.error("onQuoteRequestReject - quoteRequestReject={}", quoteRequestReject, e);
        }
    }

    @Override
    public void onQuoteCancel(QuoteCancel quoteCancel) {
        try {
            LOG.info("onQuoteCancel -> quoteCancel={}", quoteCancel);
            Session session = sessionTracker.getSession(quoteCancel.getQuote().getQuoteRequest().getSessionId());
            if ( session.getTradingListener() != null ) {
                session.getTradingListener().onQuoteCancel(quoteCancel);
            }
            LOG.info("onQuoteCancel <-");
        } catch (Exception e) {
            LOG.error("onQuoteCancel - quoteCancel={}", quoteCancel, e);
        }
    }

    @Override
    public void updateDevice(Device device) {
        try {
            LOG.info("updateDevice -> device={}", device);
            synchPersistenceManager.updateDevice(device);
            Event event = eventFactory.create(EventType.DEVICE_INFO_UPDATED, device);
            eventRouter.onEvent(event);
            LOG.info("updateDevice <-");
        } catch (Exception e) {
            LOG.error("updateDevice - device={}", device, e);
        }
    }

	public void jmxOrderStatusCancel(String orderId, String remarks) {
		try {
			LOG.info("jmxOrderStatusCancel -> orderId={},remarks={}", orderId, remarks);
			if (orderId != null) {
				Order order = queryService.getOrderByOrderId(orderId);
				if (order != null) {
					order.setState(OrderState.CANCELLED);
					order.setRemarks(remarks);
					asynchPersistenceClient.persistOrder(order);
					orderRepository.remove(order);
				}
			} else {
				LOG.warn("jmxOrderStatusCancel <- orderId={} not found in DB", orderId);
			}
			LOG.info("jmxOrderStatusCancel <- orderId={},remarks={}", orderId, remarks);
		} catch (Exception exception) {
			LOG.error("jmxOrderStatusCancel orderId={} remarks={}", orderId, remarks, exception);
			throw new RuntimeException(exception);
		}
	}

	private void assertSessionIsActive(String sessionId) {
	    if ( null == sessionTracker.getSession(sessionId) ) {
	        LOG.error("assertSessionIsActive - sessionId={} not found in the system", sessionId);
	        throw new RuntimeException(String.format("assertSessionIsActive - sessionId=%s not found in the system", sessionId));
	    }
	}

    @Override
    public void bookAggregatedPositon(String sessionId, String aggregatedPositionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_BOOK_BOOKING_AGGREGATED_POSITION);
        dealerController.bookAggregatedPosition(getDealerUserFromSessionId(sessionId), aggregatedPositionId);
    }

    @Override
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String sessionId) {
    	LOG.info("getOpenBookingAggregatedPositions -> sessionId={}", sessionId);
    	privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BOOKING_AGGREGATED_POSITION);

    	CommandRequest request = this.commandRequestFactory.createGetOpenBookingAggregatedPositionsCommandRequest();
    	CommandResponse response = this.commandCenter.sendCommandRequest(request);
    	if (response.isError()) {
    		LOG.error("getOpenBookingAggregatedPositions - Error getting open booking aggregated positions from instance {}: {}", request.getToAddress(), response.getErrorMessage());
    		throw new RuntimeException(response.getErrorMessage());
    	}
    	
    	@SuppressWarnings("unchecked")
    	List<BookingAggregatedPosition> openAggregatedPositions = (List<BookingAggregatedPosition>) response.getPayload();
    	LOG.info("getOpenBookingAggregatedPositions <- openAggregatedPositions={}", openAggregatedPositions);
		return openAggregatedPositions;
    }

    @Override
    public List<BookingAggregatedPosition> getClosedBookingAggregatedPositions(String sessionId, ZonedDateTime from, ZonedDateTime to) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BOOKING_AGGREGATED_POSITION);
        return dealerController.getClosedAggregatedPositions(getDealerUserFromSessionId(sessionId), from, to);
    }

    @Override
    public BookingAggregatedPosition getBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BOOKING_AGGREGATED_POSITION);
        return dealerController.getBookingAggregatedPosition(getDealerUserFromSessionId(sessionId), aggregatedPositionId);
    }

    @Override
    public List<Order> getOrdersFromBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BOOKING_AGGREGATED_POSITION);
        return dealerController.getOrdersFromAggregatedPosition(getDealerUserFromSessionId(sessionId), aggregatedPositionId);
    }

    @Override
    public List<BookingAggregationInstruction> getBookingAggregationInstructions(String sessionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BOOKING_AGGREGATION_INSTRUCTION);
        return dealerController.getAggregationInstructions(sessionId);
    }

    @Override
    public void deleteBookingAggregationInstruction(String sessionId, String aggregationInstructionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_DELETE_BOOKING_AGGREGATION_INSTRUCTION);
        dealerController.deleteBookingAggregationInstruction(getDealerUserFromSessionId(sessionId), aggregationInstructionId);
    }

    @Override
    public void createBookingAggregationInstruction(String sessionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition,
            long maximumMillisecondsOpen, Double maximumMarketDeviation) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BOOKING_AGGREGATION_INSTRUCTION);
        dealerController.createBookingAggregationInstruction(getDealerUserFromSessionId(sessionId), buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen, maximumMarketDeviation);
    }

    @Override
    public void updateBookingAggregationInstruction(String sessionId, String aggregationInstructionId, String buId, Channel channel, String currencyPairId,
            double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BOOKING_AGGREGATION_INSTRUCTION);
        dealerController.updateBookingAggregationInstruction(getDealerUserFromSessionId(sessionId), aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition,
                maximumMillisecondsOpen, maximumMarketDeviation);
    }

    @Override
    public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId, String buId) {
        return dealerController.getBusinessUnitLimitPosition(sessionId, buId);
    }



    @Override
    public void updateBUDistributionConfiguration(String sessionId, String buId, Channel channel, long maximumUpdatesPerSecond, ValidityMode validityMode,
            long maximumDelay, long maximumDepth, boolean bookingExecutionReportEnabled) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BU_DISTRIBUTION_CONFIGURATION);
        dealerController.updateBUDistributionConfiguration(sessionId,getDealerUserFromSessionId(sessionId), buId, channel, maximumUpdatesPerSecond, validityMode, maximumDelay, maximumDepth,bookingExecutionReportEnabled);
    }



    @Override
    public void deleteBUDistributionConfiguration(String sessionId, String buId, Channel channel) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BU_DISTRIBUTION_CONFIGURATION);
        dealerController.deleteBUDistributionConfiguration(sessionId,getDealerUserFromSessionId(sessionId), buId, channel);
    }



    @Override
    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BU_DISTRIBUTION_CONFIGURATION);
        return dealerController.getAllBUDistributionConfigurations(sessionId);
    }



    @Override
    public void updateOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId, Double pvt) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BUSINESSUNIT_PRICE_VARIATION_THRESHOLD);
        dealerController.updateOverridePriceVariationThreshold(getDealerUserFromSessionId(sessionId),businessUnitId,currencyPairId,pvt);
    }



    @Override
    public void deleteOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BUSINESSUNIT_PRICE_VARIATION_THRESHOLD);
        dealerController.deleteOverridePriceVariationThreshold(getDealerUserFromSessionId(sessionId),businessUnitId,currencyPairId);
    }

    @Override
    public BusinessUnitExposure getBUExposure(String sessionId, String businessUnitId) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_VIEW_BU_EXPOSURE);
        return dealerController.getBUExposure(getDealerUserFromSessionId(sessionId),businessUnitId);
    }

    @Override
    public void updateInstancePrimaryStatus(String sessionId, String regionId, String instanceId, PrimaryStatus primaryStatus) {
        privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_MANAGE_CLUSTER);
        dealerController.updateInstancePrimaryStatus(getDealerUserFromSessionId(sessionId), regionId, instanceId, primaryStatus);
    }

    /** for testing purposes only */

    public OrderMonitorEngine getOrderMonitorEngine() {
        return orderMonitorEngine;
    }

    public AutoHedger getAutoHedger() {
        return autoHedger;
    }

    public AggregatedBookingEngine getAggregatedBookingEngine() {
        return aggregatedBookingEngine;
    }

    public Orchestrator getOrchestrator() {
        return orchestrator;
    }

	@Override
	public List<AutoHedgerPosition> getAllAutoHedgerPositions() {
		LOG.info("getAllAutoHedgerPositions -> ");
		CommandRequest request = this.commandRequestFactory.createGetAllAutoHedgerPositionsCommandRequest();
		CommandResponse response =  this.commandCenter.sendCommandRequest(request);
        if (response.isError()) {
    		LOG.error("getAllAutoHedgerPositions - Error getting all autohedger positions from instance {}: {}", request.getToAddress(), response.getErrorMessage());
        	throw new RuntimeException(response.getErrorMessage());
        }

        @SuppressWarnings("unchecked")
		List<AutoHedgerPosition> positions = (List<AutoHedgerPosition>) response.getPayload();
        LOG.info("getAllAutoHedgerPositions <- positions.size={}", positions.size());
		return positions;
	}

	@Override
	public void updateForwardCurve(String sessionId,String currencyPairId, Tenor tenor, Double interestRate) {
		 privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_CURRENCYPAIR_FORWARD_CURVE);
	     dealerController.updateForwardCurve(getDealerUserFromSessionId(sessionId),currencyPairId,tenor,interestRate);
	}

	@Override
	public void updateBusinessUnitForwardTrading(String sessionId, String businessUnitId,
			boolean forwardTradadingEnabled) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BU_FORWARD_ENABLED_FLAG);
		dealerController.updateBusinessUnitForwardTrading(getDealerUserFromSessionId(sessionId), businessUnitId, forwardTradadingEnabled);
	}

	@Override
	public void updateBusinessUnitForwardTradingCategory(String sessionId, String businessUnitId, String category) {
		privilegeChecker.assertPrivilege(getDealerUserFromSessionId(sessionId), Privilege.WTA_DEALER_UPDATE_BU_FORWARD_TRADING_CATEGORY);
		dealerController.updateBusinessUnitForwardTradingCategory(getDealerUserFromSessionId(sessionId), businessUnitId, category);
	}
}
